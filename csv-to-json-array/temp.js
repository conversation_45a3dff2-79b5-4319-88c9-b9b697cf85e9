import fs from 'fs';
import { parse } from 'csv-parse/sync';

(async () => {
    const csv = fs.readFileSync('dumpa.csv', 'utf8');
    const records = parse(csv, {
        columns: true,
        skip_empty_lines: true,
        trim: true
    });
    console.log(records);
    fs.writeFileSync('dumpa.json', JSON.stringify(records.map((record) => {
        let parsedMessageBody;
        try {
            parsedMessageBody = record['Message Body'] ? JSON.parse(record['Message Body']) : null;
        } catch (e) {
            parsedMessageBody = record['Message Body']; // fallback to original string if not valid JSON
        }
        return {
            ...record,
            'Message Body': parsedMessageBody
        };
    }), null, 2));
})();