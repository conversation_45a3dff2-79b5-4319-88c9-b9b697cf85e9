import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
    vus: 50,
    duration: '30s',
};

const BASE_URL = 'http://localhost:9000';
const PUBLISHABLE_KEY = 'pk_a6ca2f8ad7d014e0ab59d5c593c60a3b2ab1a889bc59a2625562d0521a45247b';
const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6InVzZXJfMDFKVFNIVDFGUkJXWVBRUUo1RFhaM1FOWDIiLCJhY3Rvcl90eXBlIjoidXNlciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFKVFNIVDFLUkZDWVhXQVAwV0NCREtHWDYiLCJhcHBfbWV0YWRhdGEiOnsidXNlcl9pZCI6InVzZXJfMDFKVFNIVDFGUkJXWVBRUUo1RFhaM1FOWDIifSwiaWF0IjoxNzUzNjk3NTgzLCJleHAiOjE3NTM3ODM5ODN9.c6BlwO3Nk1SxK2URDzeusSF1Q8-S2MDjrlM2qDSO-pw';

export default function () {
    let headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_KEY,
        'Authorization': AUTH_TOKEN,
    };

    // 1. Create Cart
    let createRes = http.post(`${BASE_URL}/store/custom-carts`, JSON.stringify({ items: [] }), { headers });
    check(createRes, { 'Cart created': (r) => r.status === 200 });

    let cartId;
    try {
        cartId = JSON.parse(createRes.body).data.cart.id;
    } catch (e) {
        console.error('Failed to parse cart ID');
        return;
    }

    // 2. Add Line Item
    let addRes = http.post(`${BASE_URL}/store/carts/${cartId}/custom-line-items`, JSON.stringify({
        variant_id: '38757612',
        quantity: 1,
    }), { headers });
    check(addRes, { 'Line item added': (r) => r.status === 200 });

    // Use dummy or extracted line item id if returned
    let lineItemId = 'cali_dummy123456'; // Replace with actual one if needed from response

    // 3. Update Line Item
    let updateRes = http.put(`${BASE_URL}/store/carts/${cartId}/custom-line-items/${lineItemId}`, JSON.stringify({
        quantity: 1,
    }), { headers });
    check(updateRes, { 'Line item updated': (r) => r.status === 200 || r.status === 404 });

    // 4. Delete Line Item
    let deleteRes = http.del(`${BASE_URL}/store/carts/${cartId}/custom-line-items/${lineItemId}`, null, { headers });
    check(deleteRes, { 'Line item deleted': (r) => r.status === 200 || r.status === 404 });

    sleep(1); // Optional pause between iterations
}
