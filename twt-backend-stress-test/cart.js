import { check } from 'k6';
import http from 'k6/http';

export let options = {
    // stages: [
    //     { duration: '10s', target: 20 },
    //     { duration: '10s', target: 40 },
    //     { duration: '10s', target: 60 },
    //     { duration: '10s', target: 80 },
    //     { duration: '10s', target: 100 },
    // ],
    // vus: 20,
    // duration: '120s',
    // vus: 50,
    // duration: '60s',
    vus: 20,
    duration: '60s',
};

const BASE_URL = 'https://uat-api.thewholetruthfoods.com';
const PUBLISHABLE_KEY = 'pk_a6ca2f8ad7d014e0ab59d5c593c60a3b2ab1a889bc59a2625562d0521a45247b';

// TWT BE: 
// const BASE_URL = "http://**************:9000";
// const PUBLISHABLE_KEY = 'pk_a6ca2f8ad7d014e0ab59d5c593c60a3b2ab1a889bc59a2625562d0521a45247b';

// AndYOU BE: 
// const BASE_URL = "http://**************:9000";
// const PUBLISHABLE_KEY = "pk_336249789d4068c6ad2e5d49277e6de1997ebc74692872dbe2864dbb7a379a69";

// local BE: 
// const BASE_URL = "http://localhost:9000";
// const PUBLISHABLE_KEY = "pk_a6ca2f8ad7d014e0ab59d5c593c60a3b2ab1a889bc59a2625562d0521a45247b";


const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6ImN1c18wMUsxOFZRRlZBWVQ4RThBMlBFMEExRDU5WSIsImFjdG9yX3R5cGUiOiJjdXN0b21lciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFLMThWUUZKRFZUME5ONEswUEgyUU1BUjAiLCJhcHBfbWV0YWRhdGEiOnsiY3VzdG9tZXJfaWQiOiJjdXNfMDFLMThWUUZWQVlUOEU4QTJQRTBBMUQ1OVkifSwiaWF0IjoxNzUzODUyNzY3LCJleHAiOjE3NTQwMjU1Njd9.u1Hq_1IORr1SaiWiL8mVwIaZUDUwX0u_jZt15VP0PSM';

export default function () {
    let headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_KEY,
        // 'Authorization': AUTH_TOKEN,
    };

    // 1. Create Cart with initial item (like your first curl example)
    let createRes = http.post(`${BASE_URL}/store/custom-carts`, JSON.stringify({
        items: [{ variant_id: "38763179", quantity: 1 }]
    }), { headers });
    // console.log(createRes);
    check(createRes, { 'Cart created': (r) => r.status === 200 });

    let cartId, initialLineItemId;
    try {
        let createData = JSON.parse(createRes.body);
        cartId = createData.data.cart.id;
        // Extract the line item ID from the initial item
        if (createData.data.cart.items && createData.data.cart.items.length > 0) {
            initialLineItemId = createData.data.cart.items[0].id;
        }
        console.log(`Created cart: ${cartId}, initial line item: ${initialLineItemId}`);
    } catch (e) {
        console.error('Failed to parse cart creation response:', e);
        return;
    }

    // 2. Replace cart items with specific items
    let replaceRes = http.post(`${BASE_URL}/store/carts/${cartId}/replace`, JSON.stringify({
        items: [
            {
                variant_id: "38710606",
                quantity: 15
            },
            {
                variant_id: "38763199",
                quantity: 2
            },
            {
                variant_id: "38765763",
                quantity: 5,
                item_quantities: [
                    {
                        variant_id: "38710619",
                        quantity: 10
                    }
                ]
            }
        ]
    }), { headers });
    check(replaceRes, { 'Cart items replaced': (r) => r.status === 200 });

    let cartItems = [];
    try {
        let replaceData = JSON.parse(replaceRes.body);
        if (replaceData.data && replaceData.data.cart && replaceData.data.cart.items) {
            cartItems = replaceData.data.cart.items;
            console.log(`Cart replaced with ${cartItems.length} items`);
        }
    } catch (e) {
        console.error('Failed to parse replace response:', e);
    }

    // 3. Find one item and update it with random quantity
    if (cartItems.length > 0) {
        // Pick a random item from the cart
        let randomIndex = Math.floor(Math.random() * cartItems.length);
        let itemToUpdate = cartItems[randomIndex];
        let randomQuantity = Math.floor(Math.random() * 10) + 1; // Random quantity between 1-10

        let updateRes = http.put(`${BASE_URL}/store/carts/${cartId}/custom-line-items/${itemToUpdate.id}`, JSON.stringify({
            quantity: randomQuantity,
        }), { headers });
        check(updateRes, { 'Line item updated': (r) => r.status === 200 });
        console.log(`Updated line item: ${itemToUpdate.id} (variant: ${itemToUpdate.variant_id}) to quantity: ${randomQuantity}`);
    }

    // 4. Delete one random line item
    if (cartItems.length > 1) { // Only delete if we have more than 1 item
        let randomIndex = Math.floor(Math.random() * cartItems.length);
        let itemToDelete = cartItems[randomIndex];

        let deleteRes = http.del(`${BASE_URL}/store/carts/${cartId}/custom-line-items/${itemToDelete.id}`, null, { headers });
        check(deleteRes, { 'Line item deleted': (r) => r.status === 200 });
        console.log(`Deleted line item: ${itemToDelete.id} (variant: ${itemToDelete.variant_id})`);
    }

    // 5. Get Cart to verify final state
    let getRes = http.get(`${BASE_URL}/store/carts/${cartId}`, { headers });
    check(getRes, { 'Cart retrieved': (r) => r.status === 200 });

    // sleep(1); // Optional pause between iterations
}
