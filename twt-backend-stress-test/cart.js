import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
    vus: 50,
    duration: '30s',
};

const BASE_URL = 'http://localhost:9000';
const PUBLISHABLE_KEY = 'pk_a6ca2f8ad7d014e0ab59d5c593c60a3b2ab1a889bc59a2625562d0521a45247b';
const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6InVzZXJfMDFKVFNIVDFGUkJXWVBRUUo1RFhaM1FOWDIiLCJhY3Rvcl90eXBlIjoidXNlciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFKVFNIVDFLUkZDWVhXQVAwV0NCREtHWDYiLCJhcHBfbWV0YWRhdGEiOnsidXNlcl9pZCI6InVzZXJfMDFKVFNIVDFGUkJXWVBRUUo1RFhaM1FOWDIifSwiaWF0IjoxNzUzNjk3NTgzLCJleHAiOjE3NTM3ODM5ODN9.c6BlwO3Nk1SxK2URDzeusSF1Q8-S2MDjrlM2qDSO-pw';

export default function () {
    let headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_KEY,
        'Authorization': AUTH_TOKEN,
    };

    // 1. Create Cart with initial item (like your first curl example)
    let createRes = http.post(`${BASE_URL}/store/custom-carts`, JSON.stringify({
        items: [
            {
                variant_id: "38763179",
                quantity: 1
            }
        ]
    }), { headers });
    check(createRes, { 'Cart created': (r) => r.status === 200 });

    let cartId, initialLineItemId;
    try {
        let createData = JSON.parse(createRes.body);
        cartId = createData.data.cart.id;
        // Extract the line item ID from the initial item
        if (createData.data.cart.items && createData.data.cart.items.length > 0) {
            initialLineItemId = createData.data.cart.items[0].id;
        }
        console.log(`Created cart: ${cartId}, initial line item: ${initialLineItemId}`);
    } catch (e) {
        console.error('Failed to parse cart creation response:', e);
        return;
    }

    // 2. Add another line item (like your second curl example)
    let addRes = http.post(`${BASE_URL}/store/carts/${cartId}/custom-line-items`, JSON.stringify({
        variant_id: '38757612',
        quantity: 1,
    }), { headers });
    check(addRes, { 'Line item added': (r) => r.status === 200 });

    let newLineItemId;
    try {
        let addData = JSON.parse(addRes.body);
        // Extract the new line item ID from the response
        if (addData.data && addData.data.cart && addData.data.cart.items) {
            // Find the newly added item (should be the last one or find by variant_id)
            let newItem = addData.data.cart.items.find(item => item.variant_id === '38757612');
            if (newItem) {
                newLineItemId = newItem.id;
                console.log(`Added new line item: ${newLineItemId}`);
            }
        }
    } catch (e) {
        console.error('Failed to parse add line item response:', e);
    }

    // 3. Update Line Item (use the newly added item if available, otherwise use initial item)
    let lineItemToUpdate = newLineItemId || initialLineItemId;
    if (lineItemToUpdate) {
        let updateRes = http.put(`${BASE_URL}/store/carts/${cartId}/custom-line-items/${lineItemToUpdate}`, JSON.stringify({
            quantity: 2,
        }), { headers });
        check(updateRes, { 'Line item updated': (r) => r.status === 200 });
        console.log(`Updated line item: ${lineItemToUpdate}`);
    }

    // 4. Delete Line Item (delete the newly added item if available)
    if (newLineItemId) {
        let deleteRes = http.del(`${BASE_URL}/store/carts/${cartId}/custom-line-items/${newLineItemId}`, null, { headers });
        check(deleteRes, { 'Line item deleted': (r) => r.status === 200 });
        console.log(`Deleted line item: ${newLineItemId}`);
    }

    // 5. Get Cart to verify final state
    let getRes = http.get(`${BASE_URL}/store/carts/${cartId}`, { headers });
    check(getRes, { 'Cart retrieved': (r) => r.status === 200 });

    sleep(1); // Optional pause between iterations
}
