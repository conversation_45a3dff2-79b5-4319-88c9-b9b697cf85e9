import http from 'k6/http';
import { check, sleep } from 'k6';
import { randomIntBetween } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';

export const options = {
    vus: 1,
    duration: '10s',
};

const BASE_URL = 'http://localhost:9000';
const API_KEY = 'pk_a6ca2f8ad7d014e0ab59d5c593c60a3b2ab1a889bc59a2625562d0521a45247b';
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6ImN1c18wMUsxOFZRRlZBWVQ4RThBMlBFMEExRDU5WSIsImFjdG9yX3R5cGUiOiJjdXN0b21lciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFLMThWUUZKRFZUME5ONEswUEgyUU1BUjAiLCJhcHBfbWV0YWRhdGEiOnsiY3VzdG9tZXJfaWQiOiJjdXNfMDFLMThWUUZWQVlUOEU4QTJQRTBBMUQ1OVkifSwiaWF0IjoxNzUzODUyNzY3LCJleHAiOjE3NTQwMjU1Njd9.u1Hq_1IORr1SaiWiL8mVwIaZUDUwX0u_jZt15VP0PSM';

const headers = {
    'Accept': 'application/json',
    'Accept-Charset': 'application/json',
    'Content-Type': 'application/json',
    'x-publishable-api-key': API_KEY,
    'Authorization': `Bearer ${AUTH_TOKEN}`,
};

export default function () {
    // 1. Create new cart with empty payload
    const createPayload = JSON.stringify({});

    const createRes = http.post(`${BASE_URL}/store/custom-carts`, createPayload, { headers });

    check(createRes, {
        'create status is 200': (r) => r.status === 200,
        'create response is json': (r) => r.headers['Content-Type'] && r.headers['Content-Type'].includes('application/json'),
    });

    let cartId;
    let cartItems;

    try {
        const body = createRes.json();
        cartId = body?.data?.cart?.id;
        cartItems = body?.data?.cart?.items;
    } catch (e) {
        console.error('Failed to parse create response:', e, createRes.body);
        return;
    }

    if (!cartId) {
        console.error('Missing cartId at create step', { cartId });
        return; // Can't proceed without proper cart
    }

    // 2. Replace cart with specific items (only variant_id and quantity)
    const replacePayload = JSON.stringify({
        items: [
            {
                variant_id: "38710606",
                quantity: 15
            },
            {
                variant_id: "38763199",
                quantity: 2
            },
            {
                variant_id: "38765763",
                quantity: 5
            }
        ]
    });

    const replaceUrl = `${BASE_URL}/store/carts/${cartId}/replace`;
    const replaceRes = http.post(replaceUrl, replacePayload, { headers });

    check(replaceRes, {
        'replace status is 200': (r) => r.status === 200,
        'replace response is json': (r) => r.headers['Content-Type'] && r.headers['Content-Type'].includes('application/json'),
    });

    let replacedItems = [];
    try {
        const replaceBody = replaceRes.json();
        replacedItems = replaceBody?.data?.cart?.items || [];
        console.log(`Cart replaced with ${replacedItems.length} items`);
    } catch (e) {
        console.error('Failed to parse replace response:', e, replaceRes.body);
    }

    // 3. Update one item's quantity using /replace
    if (replacedItems.length > 0) {
        // Pick a random item and update its quantity
        const randomIndex = randomIntBetween(0, replacedItems.length - 1);
        const itemToUpdate = replacedItems[randomIndex];
        const randomQuantity = randomIntBetween(1, 10); // Random quantity between 1-10

        // Create updated items array with one item having new quantity
        const updatedItems = replacedItems.map((item, index) => ({
            variant_id: item.variant_id,
            quantity: index === randomIndex ? randomQuantity : item.quantity
        }));

        const updatePayload = JSON.stringify({ items: updatedItems });
        const updateRes = http.post(replaceUrl, updatePayload, { headers });

        check(updateRes, {
            'update item status is 200': (r) => r.status === 200,
            'update item response is json': (r) => r.headers['Content-Type'] && r.headers['Content-Type'].includes('application/json'),
        });

        console.log(`Updated item variant: ${itemToUpdate.variant_id} to quantity: ${randomQuantity}`);
    }

    // 4. Delete all items by passing empty array
    const deletePayload = JSON.stringify({ items: [] });
    const deleteRes = http.post(replaceUrl, deletePayload, { headers });

    check(deleteRes, {
        'delete all status is 200': (r) => r.status === 200,
        'delete all response is json': (r) => r.headers['Content-Type'] && r.headers['Content-Type'].includes('application/json'),
    });

    console.log('Deleted all items from cart');

    sleep(1);
}
