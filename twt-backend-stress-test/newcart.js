import http from 'k6/http';
import { check, sleep } from 'k6';
import { randomIntBetween } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';

export const options = {
    vus: 1,
    duration: '6s',
};

const BASE_URL = 'http://localhost:9000';
const API_KEY = 'pk_a6ca2f8ad7d014e0ab59d5c593c60a3b2ab1a889bc59a2625562d0521a45247b';
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6ImN1c18wMUsxOFZRRlZBWVQ4RThBMlBFMEExRDU5WSIsImFjdG9yX3R5cGUiOiJjdXN0b21lciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFLMThWUUZKRFZUME5ONEswUEgyUU1BUjAiLCJhcHBfbWV0YWRhdGEiOnsiY3VzdG9tZXJfaWQiOiJjdXNfMDFLMThWUUZWQVlUOEU4QTJQRTBBMUQ1OVkifSwiaWF0IjoxNzUzODUyNzY3LCJleHAiOjE3NTQwMjU1Njd9.u1Hq_1IORr1SaiWiL8mVwIaZUDUwX0u_jZt15VP0PSM';

const headers = {
    'Accept': 'application/json',
    'Accept-Charset': 'application/json',
    'Content-Type': 'application/json',
    'x-publishable-api-key': API_KEY,
    'Authorization': `Bearer ${AUTH_TOKEN}`,
};

export default function () {
    // 1. Create new cart with empty payload
    const createPayload = JSON.stringify({});

    const createRes = http.post(`${BASE_URL}/store/custom-carts`, createPayload, { headers });

    check(createRes, {
        'create status is 200': (r) => r.status === 200,
        'create response is json': (r) => r.headers['Content-Type'] && r.headers['Content-Type'].includes('application/json'),
    });

    let cartId;
    let cartItems;

    try {
        const body = createRes.json();
        cartId = body?.data?.cart?.id;
        cartItems = body?.data?.cart?.items;
    } catch (e) {
        console.error('Failed to parse create response:', e, createRes.body);
        return;
    }

    if (!cartId) {
        console.error('Missing cartId at create step', { cartId });
        return; // Can't proceed without proper cart
    }

    // 2. Update cart with items including their IDs from the create response
    const updatePayload = JSON.stringify({
        items: [
            {
                id: cartItems?.find(item => item?.variant?.id === "38710606")?.id || 'cali_01K1X6RSWXPN8VDVCF395FGGWH',
                variant_id: "38710606",
                quantity: 15
            },
            {
                id: cartItems?.find(item => item?.variant?.id === "38763199")?.id || 'cali_01K1X6RSWXHY6BHSY4C1FHX9H6',
                variant_id: "38763199",
                quantity: 2
            },
            {
                id: cartItems?.find(item => item?.variant?.id === "38765763")?.id || 'cali_01K1X6RSWX2TVX4C6YQEKBZXK0',
                variant_id: "38765763",
                quantity: 5,
                item_quantities: [
                    {
                        variant_id: "38710619",
                        quantity: 10
                    }
                ]
            }
        ]
    });

    const replaceUrl = `${BASE_URL}/store/carts/${cartId}/replace?fields=total%2Cid%2Citems.id%2Citems.unit_price%2Citems.quantity%2Citems.variant.id%2Citems.variant.product.id%2C`;

    const updateRes = http.post(replaceUrl, updatePayload, { headers });

    check(updateRes, {
        'update status is 200': (r) => r.status === 200,
        'update response is json': (r) => r.headers['Content-Type'] && r.headers['Content-Type'].includes('application/json'),
    });

    // 3. Delete all items using empty items array
    const deletePayload = JSON.stringify({
        items: []
    });

    const deleteRes = http.post(replaceUrl, deletePayload, { headers });

    check(deleteRes, {
        'delete status is 200': (r) => r.status === 200,
        'delete response is json': (r) => r.headers['Content-Type'] && r.headers['Content-Type'].includes('application/json'),
    });

    sleep(1);
}
