{"version": "5.6.0", "name": "csv-parse", "description": "CSV parsing implementing the Node.js `stream.Transform` API", "keywords": ["csv", "parse", "parser", "convert", "tsv", "stream", "backend", "frontend"], "author": "<PERSON> <<EMAIL>> (https://www.adaltas.com)", "contributors": ["<PERSON> <<EMAIL>> (https://www.adaltas.com)", "<PERSON> (https://github.com/willwhite)", "<PERSON> (https://github.com/justinlatimer)", "jonseymour (https://github.com/jonseymour)", "pascalopitz (https://github.com/pascalopitz)", "<PERSON> (https://github.com/jpschorr)", "<PERSON><PERSON> (https://github.com/eladb)", "<PERSON> (https://github.com/phipla)", "<PERSON> (https://github.com/timoxley)", "<PERSON> (https://github.com/<PERSON><PERSON>)", "<PERSON><PERSON><PERSON> (https://github.com/topliceanu)", "Visup (https://github.com/visup)", "<PERSON> (https://github.com/evdb)", "<PERSON> (https://github.com/dougwilson)", "<PERSON> (https://github.com/Joeasaurus)", "<PERSON> (https://github.com/markstos)"], "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./sync": {"import": {"types": "./lib/sync.d.ts", "default": "./lib/sync.js"}, "require": {"types": "./dist/cjs/sync.d.cts", "default": "./dist/cjs/sync.cjs"}}, "./stream": {"import": {"types": "./lib/stream.d.ts", "default": "./lib/stream.js"}, "require": {"types": "./dist/cjs/stream.d.cts", "default": "./dist/cjs/stream.cjs"}}, "./browser/esm": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "./browser/esm/sync": {"types": "./dist/esm/sync.d.ts", "default": "./dist/esm/sync.js"}}, "devDependencies": {"@eslint/js": "^9.15.0", "@rollup/plugin-node-resolve": "^15.3.0", "@types/mocha": "^10.0.9", "@types/node": "^22.9.1", "coffeescript": "^2.7.0", "csv-generate": "^4.4.2", "csv-spectrum": "^2.0.0", "each": "^2.7.2", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-mocha": "^10.5.0", "eslint-plugin-prettier": "^5.2.1", "mocha": "^10.8.2", "pad": "^3.3.0", "prettier": "^3.3.3", "rollup": "^4.27.3", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-node-globals": "^1.4.0", "should": "^13.2.3", "stream-transform": "^3.3.3", "ts-node": "^10.9.2", "typescript": "^5.6.3"}, "files": ["dist", "lib"], "homepage": "https://csv.js.org/parse", "license": "MIT", "main": "./dist/cjs/index.cjs", "mocha": {"inline-diffs": true, "loader": "./test/loaders/all.js", "recursive": true, "reporter": "spec", "require": ["should"], "throw-deprecation": false, "timeout": 40000}, "lint-staged": {"*.js": "npm run lint:fix", "*.md": "prettier -w"}, "repository": {"type": "git", "url": "https://github.com/adaltas/node-csv.git", "directory": "packages/csv-parse"}, "scripts": {"build": "npm run build:rollup && npm run build:ts", "build:rollup": "npx rollup -c", "build:ts": "cp lib/index.d.ts dist/cjs/index.d.cts && cp lib/sync.d.ts dist/cjs/sync.d.cts && cp lib/*.ts dist/esm", "postbuild:ts": "find dist/cjs -name '*.d.cts' -exec sh -c \"sed -i \"s/\\.js'/\\.cjs'/g\" {} || sed -i '' \"s/\\.js'/\\.cjs'/g\" {}\" \\;", "lint:check": "eslint", "lint:fix": "eslint --fix", "lint:ts": "tsc --noEmit true", "preversion": "npm run build && git add dist", "test": "mocha 'test/**/*.{coffee,ts}'", "test:legacy": "mocha --ignore test/api.web_stream.coffee --ignore test/api.web_stream.ts --ignore test/api.stream.finished.coffee --ignore test/api.stream.iterator.coffee --loader=./test/loaders/legacy/all.js 'test/**/*.{coffee,ts}'"}, "type": "module", "types": "dist/esm/index.d.ts", "typesVersions": {"*": {".": ["dist/esm/index.d.ts"], "sync": ["dist/esm/sync.d.ts"], "browser/esm": ["dist/esm/index.d.ts"], "browser/esm/sync": ["dist/esm/sync.d.ts"]}}, "gitHead": "cc1235a58de98dd9eab0665c7b1d03213e9633c7"}